import { EventEmitter } from 'events';
import { storage } from './storage';
import { log } from './vite';
import type { <PERSON><PERSON>, Mentee, Organization, Match } from '@shared/schema';
import {
  sendInvitationEmail,
  sendIntroductionEmail,
  sendFollowUpEmail,
  sendFeedbackRequestEmails,
  sendMentorSubmissionNotificationEmail,
  sendMenteeSubmissionNotificationEmail
} from './emails';

export interface EmailData {
  mentorId?: number;
  menteeId?: number;
  matchId?: number;
  organizationId?: number;
  email?: string;
  userType?: 'mentor' | 'mentee';
  customMessage?: string;
  formUrl?: string;
  adminEmails?: string[];
  [key: string]: any;
}

export interface EmailEventData {
  emailType: string;
  data: EmailData;
}

export class EmailService extends EventEmitter {
  constructor() {
    super();
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Listen for email events
    this.on('email:send', this.handleSendEmail.bind(this));
  }

  private async handleSendEmail(eventData: EmailEventData) {
    try {
      await this.sendEmail(eventData.emailType, eventData.data);
    } catch (error) {
      log(`Error sending email: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Don't throw error to prevent app crashes
    }
  }

  async sendEmail(emailType: string, data: EmailData): Promise<boolean> {
    try {
      switch (emailType) {
        case 'invitation':
          return await this.handleInvitationEmail(data);
        case 'introduction':
          return await this.handleIntroductionEmail(data);
        case 'follow_up':
          return await this.handleFollowUpEmail(data);
        case 'feedback_request':
          return await this.handleFeedbackRequestEmail(data);
        case 'mentor_submission':
          return await this.handleMentorSubmissionEmail(data);
        case 'mentee_submission':
          return await this.handleMenteeSubmissionEmail(data);
        default:
          log(`Unknown email type: ${emailType}`);
          return false;
      }
    } catch (error) {
      log(`Failed to send ${emailType} email: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }

  private async handleInvitationEmail(data: EmailData): Promise<boolean> {
    if (!data.email || !data.userType || !data.organizationId) {
      log('Missing required data for invitation email');
      return false;
    }

    const organization = await storage.getOrganization(data.organizationId);
    if (!organization) {
      log('Organization not found for invitation email');
      return false;
    }

    return await sendInvitationEmail(
      data.email,
      data.userType,
      organization,
      data.customMessage || '',
      data.formUrl
    );
  }

  private async handleIntroductionEmail(data: EmailData): Promise<boolean> {
    if (!data.matchId) {
      log('Missing match ID for introduction email');
      return false;
    }

    const match = await storage.getMatch(data.matchId);
    if (!match) {
      log('Match not found for introduction email');
      return false;
    }

    const [mentor, mentee, organization] = await Promise.all([
      storage.getMentor(match.mentorId),
      storage.getMentee(match.menteeId),
      storage.getOrganization(match.organizationId)
    ]);

    if (!mentor || !mentee || !organization) {
      log('Missing mentor, mentee, or organization for introduction email');
      return false;
    }

    return await sendIntroductionEmail(mentor, mentee, organization, match);
  }

  private async handleFollowUpEmail(data: EmailData): Promise<boolean> {
    if (!data.mentorId || !data.menteeId || !data.organizationId) {
      log('Missing required data for follow-up email');
      return false;
    }

    const [mentor, mentee, organization] = await Promise.all([
      storage.getMentor(data.mentorId),
      storage.getMentee(data.menteeId),
      storage.getOrganization(data.organizationId)
    ]);

    if (!mentor || !mentee || !organization) {
      log('Missing mentor, mentee, or organization for follow-up email');
      return false;
    }

    return await sendFollowUpEmail(mentor, mentee, organization);
  }

  private async handleFeedbackRequestEmail(data: EmailData): Promise<boolean> {
    if (!data.mentorId || !data.menteeId || !data.organizationId) {
      log('Missing required data for feedback request email');
      return false;
    }

    const [mentor, mentee, organization] = await Promise.all([
      storage.getMentor(data.mentorId),
      storage.getMentee(data.menteeId),
      storage.getOrganization(data.organizationId)
    ]);

    if (!mentor || !mentee || !organization) {
      log('Missing mentor, mentee, or organization for feedback request email');
      return false;
    }

    return await sendFeedbackRequestEmails(mentor, mentee, organization);
  }

  private async handleMentorSubmissionEmail(data: EmailData): Promise<boolean> {
    if (!data.mentorId || !data.organizationId || !data.adminEmails) {
      log('Missing required data for mentor submission email');
      return false;
    }

    const [mentor, organization] = await Promise.all([
      storage.getMentor(data.mentorId),
      storage.getOrganization(data.organizationId)
    ]);

    if (!mentor || !organization) {
      log('Missing mentor or organization for submission email');
      return false;
    }

    return await sendMentorSubmissionNotificationEmail(mentor, organization, data.adminEmails);
  }

  private async handleMenteeSubmissionEmail(data: EmailData): Promise<boolean> {
    if (!data.menteeId || !data.organizationId || !data.adminEmails) {
      log('Missing required data for mentee submission email');
      return false;
    }

    const [mentee, organization] = await Promise.all([
      storage.getMentee(data.menteeId),
      storage.getOrganization(data.organizationId)
    ]);

    if (!mentee || !organization) {
      log('Missing mentee or organization for submission email');
      return false;
    }

    return await sendMenteeSubmissionNotificationEmail(mentee, organization, data.adminEmails);
  }

  // Helper methods to emit email events
  emitInvitationEmail(email: string, userType: 'mentor' | 'mentee', organizationId: number, customMessage?: string, formUrl?: string) {
    this.emit('email:send', {
      emailType: 'invitation',
      data: {
        email,
        userType,
        organizationId,
        customMessage,
        formUrl
      }
    });
  }

  emitIntroductionEmail(matchId: number) {
    this.emit('email:send', {
      emailType: 'introduction',
      data: { matchId }
    });
  }

  emitFollowUpEmail(mentorId: number, menteeId: number, organizationId: number) {
    this.emit('email:send', {
      emailType: 'follow_up',
      data: {
        mentorId,
        menteeId,
        organizationId
      }
    });
  }

  emitFeedbackRequestEmail(mentorId: number, menteeId: number, organizationId: number) {
    this.emit('email:send', {
      emailType: 'feedback_request',
      data: {
        mentorId,
        menteeId,
        organizationId
      }
    });
  }

  emitMentorSubmissionEmail(mentorId: number, organizationId: number, adminEmails: string[]) {
    this.emit('email:send', {
      emailType: 'mentor_submission',
      data: {
        mentorId,
        organizationId,
        adminEmails
      }
    });
  }

  emitMenteeSubmissionEmail(menteeId: number, organizationId: number, adminEmails: string[]) {
    this.emit('email:send', {
      emailType: 'mentee_submission',
      data: {
        menteeId,
        organizationId,
        adminEmails
      }
    });
  }
}

// Create and export a singleton instance
export const emailService = new EmailService();
