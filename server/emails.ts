import { Resend } from 'resend';
import { <PERSON><PERSON>, Mentee, Organization, Match } from '@shared/schema';
import {
  generateIntroductionEmail,
  generateFollowUpEmail,
  generateInvitationEmail,
  generateMentorFeedbackEmail,
  generateMenteeFeedbackEmail
} from './email-templates';

// Initialize Resend with the API key (optional for development)
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// For development/testing, we need to use <PERSON>send's test emails instead of example.com domains
function getSafeEmailAddress(email: string): string {
  // In development/testing, use Resen<PERSON>'s test email address
  if (email.endsWith('example.com') || !email.includes('@') || !email.includes('.')) {
    return '<EMAIL>';
  }
  return email;
}

// Build email HTML with consistent header and footer
function buildEmail(organization: Organization, bodyContent: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: ${organization.primaryColor || '#3B82F6'}; padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">Welcome to ${organization.name}!</h1>
      </div>
      <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
        ${bodyContent}
      </div>
      <div style="background-color: #f3f4f6; padding: 10px; text-align: center; font-size: 12px; color: #6b7280;">
        <p>© ${new Date().getFullYear()} ${organization.name}. All rights reserved.</p>
      </div>
    </div>
  `;
}

// Convert plain text template to HTML paragraphs
function textToHtml(text: string): string {
  return text
    .split('\n\n')
    .map(paragraph => paragraph.trim())
    .filter(paragraph => paragraph.length > 0)
    .map(paragraph => `<p>${paragraph}</p>`)
    .join('');
}

// Function to send welcome email to approved mentors
export async function sendMentorWelcomeEmail(mentor: Mentor, organization: Organization): Promise<boolean> {
  try {
    // Skip email sending if Resend is not configured
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const safeEmail = getSafeEmailAddress(mentor.email);

    const bodyContent = `
      <p>Dear ${mentor.name},</p>
      <p>We're delighted to welcome you as an approved mentor in the ${organization.name} mentorship program!</p>
      <p>Your expertise and experience will be invaluable to our mentees who are eager to learn and grow. You are now visible in our mentor pool and may be matched with mentees based on skills and interests.</p>
      <p>Here's what you can expect next:</p>
      <ul>
        <li>You'll receive notifications when you're matched with a mentee</li>
        <li>You can review potential matches and schedule sessions</li>
        <li>You'll have access to resources to help make your mentoring effective</li>
      </ul>
      <p>Thank you for your commitment to supporting the next generation of entrepreneurs and professionals.</p>
      <p>Best regards,<br>The ${organization.name} Team</p>
    `;

    const { data, error } = await resend.emails.send({
      from: `Mentor Match <<EMAIL>>`,
      to: safeEmail,
      subject: `Welcome to ${organization.name}'s Mentor Program!`,
      html: buildEmail(organization, bodyContent),
    });

    if (error) {
      console.error('Error sending mentor welcome email:', error);
      return false;
    }

    console.log('Mentor welcome email sent successfully:', data?.id);
    return true;
  } catch (error) {
    console.error('Failed to send mentor welcome email:', error);
    return false;
  }
}

// Function to send welcome email to approved mentees
export async function sendMenteeWelcomeEmail(mentee: Mentee, organization: Organization): Promise<boolean> {
  try {
    // Skip email sending if Resend is not configured
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const safeEmail = getSafeEmailAddress(mentee.email);

    const bodyContent = `
      <p>Dear ${mentee.name},</p>
      <p>Welcome to the ${organization.name} mentorship program! We're excited to have you join us as a mentee.</p>
      <p>Your application has been approved, and we're now working on finding the perfect mentor match for you based on your goals, interests, and requirements.</p>
      <p>Here's what happens next:</p>
      <ul>
        <li>You'll be notified when we've found a suitable mentor for you</li>
        <li>You'll be able to schedule your first mentoring session</li>
        <li>You'll gain access to additional resources to help you make the most of your mentorship</li>
      </ul>
      <p>We're committed to helping you grow and achieve your goals through this mentorship opportunity.</p>
      <p>Best regards,<br>The ${organization.name} Team</p>
    `;

    const { data, error } = await resend.emails.send({
      from: `Mentor Match <<EMAIL>>`,
      to: safeEmail,
      subject: `Welcome to ${organization.name}'s Mentorship Program!`,
      html: buildEmail(organization, bodyContent),
    });

    if (error) {
      console.error('Error sending mentee welcome email:', error);
      return false;
    }

    console.log('Mentee welcome email sent successfully:', data?.id);
    return true;
  } catch (error) {
    console.error('Failed to send mentee welcome email:', error);
    return false;
  }
}

// Function to send invitation email
export async function sendInvitationEmail(
  email: string,
  userType: 'mentor' | 'mentee',
  organization: Organization,
  customMessage: string = '',
  formUrl?: string
): Promise<boolean> {
  try {
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const safeEmail = getSafeEmailAddress(email);
    const templateText = generateInvitationEmail(organization, userType, customMessage);

    // Replace the placeholder link with actual form URL if provided
    let bodyContent = textToHtml(templateText);
    if (formUrl) {
      bodyContent = bodyContent.replace('[Complete Your Profile]', `<a href="${formUrl}" style="color: #3B82F6; text-decoration: none;">Complete Your Profile</a>`);
    }

    const { data, error } = await resend.emails.send({
      from: `Mentor Match <<EMAIL>>`,
      to: safeEmail,
      subject: `Invitation to Join ${organization.name}'s Mentorship Program`,
      html: buildEmail(organization, bodyContent),
    });

    if (error) {
      console.error('Error sending invitation email:', error);
      return false;
    }

    console.log('Invitation email sent successfully:', data?.id);
    return true;
  } catch (error) {
    console.error('Failed to send invitation email:', error);
    return false;
  }
}

// Function to send introduction email
export async function sendIntroductionEmail(
  mentor: Mentor,
  mentee: Mentee,
  organization: Organization,
  match: Match
): Promise<boolean> {
  try {
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const templateText = generateIntroductionEmail(mentor, mentee, organization, match);
    const bodyContent = textToHtml(templateText);

    // Send to both mentor and mentee
    const mentorEmail = getSafeEmailAddress(mentor.email);
    const menteeEmail = getSafeEmailAddress(mentee.email);

    const emailPromises = [
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: mentorEmail,
        subject: `Introduction: You've been matched with ${mentee.name}`,
        html: buildEmail(organization, bodyContent),
      }),
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: menteeEmail,
        subject: `Meet Your Mentor: ${mentor.name}`,
        html: buildEmail(organization, bodyContent),
      })
    ];

    const results = await Promise.all(emailPromises);

    if (results.some(result => result.error)) {
      console.error('Error sending introduction emails:', results.filter(r => r.error));
      return false;
    }

    console.log('Introduction emails sent successfully');
    return true;
  } catch (error) {
    console.error('Failed to send introduction emails:', error);
    return false;
  }
}

// Function to send follow-up email
export async function sendFollowUpEmail(
  mentor: Mentor,
  mentee: Mentee,
  organization: Organization
): Promise<boolean> {
  try {
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const templateText = generateFollowUpEmail(mentor, mentee, organization);
    const bodyContent = textToHtml(templateText);
    const menteeEmail = getSafeEmailAddress(mentee.email);

    const { data, error } = await resend.emails.send({
      from: `Mentor Match <<EMAIL>>`,
      to: menteeEmail,
      subject: `Follow-up: Your mentorship with ${mentor.name}`,
      html: buildEmail(organization, bodyContent),
    });

    if (error) {
      console.error('Error sending follow-up email:', error);
      return false;
    }

    console.log('Follow-up email sent successfully:', data?.id);
    return true;
  } catch (error) {
    console.error('Failed to send follow-up email:', error);
    return false;
  }
}

// Function to send feedback request emails
export async function sendFeedbackRequestEmails(
  mentor: Mentor,
  mentee: Mentee,
  organization: Organization
): Promise<boolean> {
  try {
    if (!resend) {
      console.log('Email sending skipped - RESEND_API_KEY not configured');
      return true;
    }

    const mentorTemplateText = generateMentorFeedbackEmail(mentor, mentee, organization);
    const menteeTemplateText = generateMenteeFeedbackEmail(mentor, mentee, organization);

    const mentorBodyContent = textToHtml(mentorTemplateText);
    const menteeBodyContent = textToHtml(menteeTemplateText);

    const mentorEmail = getSafeEmailAddress(mentor.email);
    const menteeEmail = getSafeEmailAddress(mentee.email);

    const emailPromises = [
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: mentorEmail,
        subject: `Feedback Request: Your session with ${mentee.name}`,
        html: buildEmail(organization, mentorBodyContent),
      }),
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: menteeEmail,
        subject: `Feedback Request: Your session with ${mentor.name}`,
        html: buildEmail(organization, menteeBodyContent),
      })
    ];

    const results = await Promise.all(emailPromises);

    if (results.some(result => result.error)) {
      console.error('Error sending feedback request emails:', results.filter(r => r.error));
      return false;
    }

    console.log('Feedback request emails sent successfully');
    return true;
  } catch (error) {
    console.error('Failed to send feedback request emails:', error);
    return false;
  }
}

// Function to send mentor form submission notification email to admins
export async function sendMentorSubmissionNotificationEmail(
  mentor: Mentor,
  organization: Organization,
  adminEmails: string[]
): Promise<boolean> {
  try {
    if (!resend || adminEmails.length === 0) {
      console.log('Email sending skipped - RESEND_API_KEY not configured or no admin emails');
      return true;
    }

    const bodyContent = `
      <p>Dear Admin,</p>
      <p>A new mentor application has been submitted and is awaiting your review.</p>
      <p><strong>Mentor Details:</strong></p>
      <ul>
        <li><strong>Name:</strong> ${mentor.name}</li>
        <li><strong>Email:</strong> ${mentor.email}</li>
        <li><strong>Title:</strong> ${mentor.title || 'Not provided'}</li>
        <li><strong>Organization:</strong> ${mentor.organization || 'Not provided'}</li>
        <li><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</li>
      </ul>
      <p>Please log in to the admin dashboard to review and approve this application.</p>
      <p>Best regards,<br>The ${organization.name} System</p>
    `;

    const emailPromises = adminEmails.map(adminEmail =>
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: getSafeEmailAddress(adminEmail),
        subject: `New Mentor Application: ${mentor.name}`,
        html: buildEmail(organization, bodyContent),
      })
    );

    const results = await Promise.all(emailPromises);

    if (results.some(result => result.error)) {
      console.error('Error sending mentor submission notification emails:', results.filter(r => r.error));
      return false;
    }

    console.log('Mentor submission notification emails sent successfully');
    return true;
  } catch (error) {
    console.error('Failed to send mentor submission notification emails:', error);
    return false;
  }
}

// Function to send mentee form submission notification email to admins
export async function sendMenteeSubmissionNotificationEmail(
  mentee: Mentee,
  organization: Organization,
  adminEmails: string[]
): Promise<boolean> {
  try {
    if (!resend || adminEmails.length === 0) {
      console.log('Email sending skipped - RESEND_API_KEY not configured or no admin emails');
      return true;
    }

    const bodyContent = `
      <p>Dear Admin,</p>
      <p>A new mentee application has been submitted and is awaiting your review.</p>
      <p><strong>Mentee Details:</strong></p>
      <ul>
        <li><strong>Name:</strong> ${mentee.name}</li>
        <li><strong>Email:</strong> ${mentee.email}</li>
        <li><strong>Goals:</strong> ${mentee.goals || 'Not provided'}</li>
        <li><strong>Industry:</strong> ${mentee.industry || 'Not provided'}</li>
        <li><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</li>
      </ul>
      <p>Please log in to the admin dashboard to review and approve this application.</p>
      <p>Best regards,<br>The ${organization.name} System</p>
    `;

    const emailPromises = adminEmails.map(adminEmail =>
      resend.emails.send({
        from: `Mentor Match <<EMAIL>>`,
        to: getSafeEmailAddress(adminEmail),
        subject: `New Mentee Application: ${mentee.name}`,
        html: buildEmail(organization, bodyContent),
      })
    );

    const results = await Promise.all(emailPromises);

    if (results.some(result => result.error)) {
      console.error('Error sending mentee submission notification emails:', results.filter(r => r.error));
      return false;
    }

    console.log('Mentee submission notification emails sent successfully');
    return true;
  } catch (error) {
    console.error('Failed to send mentee submission notification emails:', error);
    return false;
  }
}