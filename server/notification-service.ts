import { EventEmitter } from 'events';
import { storage } from './storage';
import { log } from './vite';
import type { InsertNotification, Notification } from '@shared/schema';

export interface NotificationData {
  mentorId?: number;
  menteeId?: number;
  mentorName?: string;
  menteeName?: string;
  email?: string;
  [key: string]: any;
}

export interface NotificationEventData {
  organizationId: number;
  notificationType: string;
  data: NotificationData;
}

export class NotificationService extends EventEmitter {
  constructor() {
    super();
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Listen for notification events
    this.on('notification:create', this.handleCreateNotification.bind(this));
  }

  private async handleCreateNotification(eventData: NotificationEventData) {
    try {
      await this.createNotification(
        eventData.organizationId,
        eventData.notificationType,
        eventData.data
      );
    } catch (error) {
      log(`Error creating notification: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Don't throw error to prevent app crashes
    }
  }

  async createNotification(
    organizationId: number,
    notificationType: string,
    data: NotificationData
  ): Promise<Notification | null> {
    try {
      const notificationData: InsertNotification = {
        organizationId,
        notificationType,
        data,
        markedAsRead: false,
      };

      const notification = await storage.createNotification(notificationData);
      log(`Created notification: ${notificationType} for organization ${organizationId}`);
      return notification;
    } catch (error) {
      log(`Failed to create notification: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  async getNotifications(organizationId: number): Promise<Notification[]> {
    try {
      return await storage.getNotifications(organizationId);
    } catch (error) {
      log(`Failed to get notifications: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  async markAsRead(notificationId: number): Promise<boolean> {
    try {
      const success = await storage.markNotificationAsRead(notificationId);
      if (success) {
        log(`Marked notification ${notificationId} as read`);
      }
      return success;
    } catch (error) {
      log(`Failed to mark notification as read: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }

  async markAllAsRead(organizationId: number): Promise<boolean> {
    try {
      const success = await storage.markAllNotificationsAsRead(organizationId);
      if (success) {
        log(`Marked all notifications as read for organization ${organizationId}`);
      }
      return success;
    } catch (error) {
      log(`Failed to mark all notifications as read: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }

  // Helper methods to emit notification events
  emitMentorSubmitted(organizationId: number, mentorData: { id: number; name: string; email: string }) {
    this.emit('notification:create', {
      organizationId,
      notificationType: 'mentor_submitted',
      data: {
        mentorId: mentorData.id,
        mentorName: mentorData.name,
        email: mentorData.email,
      },
    });
  }

  emitMenteeSubmitted(organizationId: number, menteeData: { id: number; name: string; email: string }) {
    this.emit('notification:create', {
      organizationId,
      notificationType: 'mentee_submitted',
      data: {
        menteeId: menteeData.id,
        menteeName: menteeData.name,
        email: menteeData.email,
      },
    });
  }

  emitMentorApproved(organizationId: number, mentorData: { id: number; name: string; email: string }) {
    this.emit('notification:create', {
      organizationId,
      notificationType: 'mentor_approved',
      data: {
        mentorId: mentorData.id,
        mentorName: mentorData.name,
        email: mentorData.email,
      },
    });
  }

  emitMenteeApproved(organizationId: number, menteeData: { id: number; name: string; email: string }) {
    this.emit('notification:create', {
      organizationId,
      notificationType: 'mentee_approved',
      data: {
        menteeId: menteeData.id,
        menteeName: menteeData.name,
        email: menteeData.email,
      },
    });
  }
}

// Create and export a singleton instance
export const notificationService = new NotificationService();
