import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Check, UserP<PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

interface NotificationData {
  mentorId?: number;
  menteeId?: number;
  mentorName?: string;
  menteeName?: string;
  email?: string;
  [key: string]: any;
}

interface Notification {
  id: number;
  organizationId: number;
  notificationType: string;
  data: NotificationData;
  markedAsRead: boolean;
  createdAt: string;
  updatedAt: string;
}

// Helper function to get notification display info
const getNotificationDisplay = (notification: Notification) => {
  const { notificationType, data } = notification;

  switch (notificationType) {
    case 'mentor_submitted':
      return {
        title: "New mentor application",
        description: `${data.mentorName} submitted a mentor application`,
        icon: <UserPlus className="h-4 w-4 text-blue-500" />,
        link: "/mentors"
      };
    case 'mentee_submitted':
      return {
        title: "New mentee application",
        description: `${data.menteeName} submitted a mentee application`,
        icon: <UserPlus className="h-4 w-4 text-blue-500" />,
        link: "/mentees"
      };
    case 'mentor_approved':
      return {
        title: "Mentor approved",
        description: `${data.mentorName} has been approved as a mentor`,
        icon: <Check className="h-4 w-4 text-green-500" />,
        link: "/mentors"
      };
    case 'mentee_approved':
      return {
        title: "Mentee approved",
        description: `${data.menteeName} has been approved as a mentee`,
        icon: <Check className="h-4 w-4 text-green-500" />,
        link: "/mentees"
      };
    default:
      return {
        title: "Notification",
        description: "You have a new notification",
        icon: <Bell className="h-4 w-4 text-gray-500" />,
        link: "/"
      };
  }
};

// Helper function to format time
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes} min ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
};

export default function NotificationDropdown() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch notifications
  const { data: notifications = [], isLoading } = useQuery({
    queryKey: ['/api/notifications'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/notifications?organizationId=1');
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      return response.json();
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Calculate unread count
  const unreadCount = notifications.filter((n: Notification) => !n.markedAsRead).length;

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/notifications/mark-all-read', { organizationId: 1 });
      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      toast({
        title: "Notifications",
        description: "All notifications have been marked as read",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to mark notifications as read",
        variant: "destructive"
      });
    }
  });

  // Mark single notification as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: number) => {
      const response = await apiRequest('POST', `/api/notifications/${notificationId}/mark-read`);
      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    }
  });

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read if not already read
    if (!notification.markedAsRead) {
      markAsReadMutation.mutate(notification.id);
    }

    // Navigate to the appropriate page
    const display = getNotificationDisplay(notification);
    navigate(display.link);
  };

  const markAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon"
          className="relative"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-[10px] text-white font-bold">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="font-normal">
          <div className="flex justify-between items-center">
            <h3 className="font-semibold">Notifications</h3>
            <p className="text-xs text-muted-foreground">
              You have {unreadCount} unread notifications
            </p>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />

        {isLoading ? (
          <div className="py-4 px-4 text-center text-sm text-gray-500">
            Loading notifications...
          </div>
        ) : notifications.length === 0 ? (
          <div className="py-4 px-4 text-center text-sm text-gray-500">
            No notifications yet
          </div>
        ) : (
          notifications.map((notification: Notification) => {
            const display = getNotificationDisplay(notification);
            return (
              <div
                key={notification.id}
                className={`cursor-pointer py-3 px-4 hover:bg-gray-50 ${
                  !notification.markedAsRead ? 'bg-blue-50' : ''
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    {display.icon}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className={`text-sm font-medium ${
                        !notification.markedAsRead ? 'text-gray-900' : 'text-gray-700'
                      }`}>
                        {display.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTime(notification.createdAt)}
                      </p>
                    </div>
                    <p className="text-xs text-gray-600">{display.description}</p>
                    {!notification.markedAsRead && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
        
        {notifications.length > 0 && unreadCount > 0 && (
          <>
            <DropdownMenuSeparator />
            <div className="text-center py-2 px-2">
              <Button
                variant="ghost"
                className="w-full text-xs h-8"
                onClick={markAllAsRead}
                disabled={markAllAsReadMutation.isPending}
              >
                {markAllAsReadMutation.isPending ? 'Marking...' : 'Mark all as read'}
              </Button>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}